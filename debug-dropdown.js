// Debug script cho language dropdown
// Copy và paste vào browser console khi ở trang http://localhost:4322

console.log('🧪 Starting Language Dropdown Debug...');

// 1. Kiểm tra button tồn tại
const languageButton = document.querySelector('[data-testid="language-selector-button"]');
console.log('Language Button:', languageButton);

if (!languageButton) {
    console.error('❌ Language button not found!');
    console.log('Available buttons:', document.querySelectorAll('button'));
} else {
    console.log('✅ Language button found');
    
    // 2. Kiểm tra event listeners
    console.log('Button onclick:', languageButton.onclick);
    
    // 3. Test click
    console.log('🖱️ Simulating click...');
    languageButton.click();
    
    // 4. Kiểm tra dropdown sau click
    setTimeout(() => {
        const dropdown = document.querySelector('[data-testid="language-dropdown"]');
        console.log('Dropdown after click:', dropdown);
        
        if (dropdown) {
            console.log('✅ Dropdown found!');
            console.log('Dropdown styles:', {
                display: window.getComputedStyle(dropdown).display,
                opacity: window.getComputedStyle(dropdown).opacity,
                visibility: window.getComputedStyle(dropdown).visibility,
                zIndex: window.getComputedStyle(dropdown).zIndex,
                position: window.getComputedStyle(dropdown).position,
                top: window.getComputedStyle(dropdown).top,
                right: window.getComputedStyle(dropdown).right
            });
        } else {
            console.error('❌ Dropdown not found after click!');
            
            // Debug: tìm tất cả elements có class liên quan
            const relatedElements = document.querySelectorAll('[class*="dropdown"], [class*="language"]');
            console.log('Related elements:', relatedElements);
        }
    }, 500);
}

// 5. Kiểm tra React state (nếu có)
setTimeout(() => {
    console.log('🔍 Checking for React DevTools...');
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('React DevTools available');
    }
}, 1000);
