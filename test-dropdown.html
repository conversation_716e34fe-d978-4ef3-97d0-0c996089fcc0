<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Language Dropdown</title>
    <script>
        // Test script để kiểm tra dropdown functionality
        function testDropdown() {
            console.log('🧪 Starting dropdown test...');
            
            // Tìm language selector button
            const languageButton = document.querySelector('[data-testid="language-selector-button"]');
            if (!languageButton) {
                console.error('❌ Language selector button not found!');
                return;
            }
            console.log('✅ Language selector button found');
            
            // Test click event
            console.log('🖱️ Simulating click on language button...');
            languageButton.click();
            
            // Kiểm tra dropdown xuất hiện sau 500ms
            setTimeout(() => {
                const dropdown = document.querySelector('[data-testid="language-dropdown"]');
                if (dropdown) {
                    console.log('✅ Dropdown appeared successfully!');
                    console.log('📊 Dropdown styles:', {
                        display: window.getComputedStyle(dropdown).display,
                        opacity: window.getComputedStyle(dropdown).opacity,
                        zIndex: window.getComputedStyle(dropdown).zIndex,
                        position: window.getComputedStyle(dropdown).position
                    });
                    
                    // Test click outside để đóng dropdown
                    console.log('🖱️ Testing click outside to close dropdown...');
                    document.body.click();
                    
                    setTimeout(() => {
                        const dropdownAfterOutsideClick = document.querySelector('[data-testid="language-dropdown"]');
                        if (!dropdownAfterOutsideClick) {
                            console.log('✅ Dropdown closed successfully after outside click!');
                        } else {
                            console.log('❌ Dropdown still visible after outside click');
                        }
                    }, 300);
                    
                } else {
                    console.error('❌ Dropdown did not appear!');
                    
                    // Debug: kiểm tra state
                    console.log('🔍 Debugging - checking for any dropdown elements...');
                    const allDropdowns = document.querySelectorAll('.language-dropdown, [class*="dropdown"]');
                    console.log('Found dropdown elements:', allDropdowns);
                }
            }, 500);
        }
        
        // Chạy test khi page load xong
        window.addEventListener('load', () => {
            setTimeout(testDropdown, 2000); // Đợi 2s để React component render xong
        });
    </script>
</head>
<body>
    <h1>Testing Language Dropdown</h1>
    <p>Open browser console to see test results.</p>
    <p>Navigate to <a href="http://localhost:4322" target="_blank">http://localhost:4322</a> and run the test.</p>
    
    <script>
        // Auto redirect to main app
        setTimeout(() => {
            window.location.href = 'http://localhost:4322';
        }, 1000);
    </script>
</body>
</html>
