// Simple test script - paste this into browser console at http://localhost:4322

console.log('🧪 Testing Language Dropdown...');

// Wait for React to load
setTimeout(() => {
  const button = document.querySelector('[data-testid="language-selector-button"]');
  
  if (button) {
    console.log('✅ Button found, clicking...');
    button.click();
    
    setTimeout(() => {
      const dropdown = document.querySelector('[data-testid="language-dropdown"]');
      if (dropdown) {
        console.log('✅ SUCCESS: Dropdown appeared!');
        console.log('Dropdown element:', dropdown);
        console.log('Dropdown computed styles:', {
          display: getComputedStyle(dropdown).display,
          opacity: getComputedStyle(dropdown).opacity,
          visibility: getComputedStyle(dropdown).visibility,
          zIndex: getComputedStyle(dropdown).zIndex
        });
      } else {
        console.log('❌ FAILED: Dropdown not found');
        console.log('All elements with "dropdown":', document.querySelectorAll('[class*="dropdown"]'));
      }
    }, 300);
  } else {
    console.log('❌ Button not found');
    console.log('All buttons:', document.querySelectorAll('button'));
  }
}, 2000);
