// Simple test script for NEW language dropdown - paste this into browser console at http://localhost:4322

console.log('🧪 Testing NEW Language Dropdown...');

// Wait for React to load
setTimeout(() => {
  // Look for language button by flag and text content
  const buttons = document.querySelectorAll('button');
  let languageButton = null;

  buttons.forEach(btn => {
    const text = btn.textContent || '';
    if (text.includes('🇻🇳') || text.includes('VI') || text.includes('EN')) {
      languageButton = btn;
    }
  });

  if (languageButton) {
    console.log('✅ Language button found:', languageButton);
    console.log('Button text:', languageButton.textContent);

    // Click the button
    console.log('🖱️ Clicking language button...');
    languageButton.click();

    setTimeout(() => {
      // Look for dropdown by checking for language options
      const dropdowns = document.querySelectorAll('div[class*="absolute"]');
      let foundDropdown = null;

      dropdowns.forEach(dropdown => {
        const text = dropdown.textContent || '';
        if (text.includes('Tiếng Việt') || text.includes('English') || text.includes('Español')) {
          foundDropdown = dropdown;
        }
      });

      if (foundDropdown) {
        console.log('✅ SUCCESS: Language dropdown appeared!');
        console.log('Dropdown element:', foundDropdown);
        console.log('Dropdown content:', foundDropdown.textContent);
        console.log('Dropdown styles:', {
          display: getComputedStyle(foundDropdown).display,
          opacity: getComputedStyle(foundDropdown).opacity,
          visibility: getComputedStyle(foundDropdown).visibility,
          zIndex: getComputedStyle(foundDropdown).zIndex,
          position: getComputedStyle(foundDropdown).position
        });

        // Test selecting a language
        const englishOption = Array.from(foundDropdown.querySelectorAll('button')).find(btn =>
          btn.textContent?.includes('English')
        );

        if (englishOption) {
          console.log('🖱️ Testing language selection - clicking English...');
          englishOption.click();

          setTimeout(() => {
            const newButtonText = languageButton.textContent;
            if (newButtonText?.includes('EN')) {
              console.log('✅ SUCCESS: Language changed to English!');
            } else {
              console.log('❌ Language selection may not have worked. Button text:', newButtonText);
            }
          }, 200);
        }

      } else {
        console.log('❌ FAILED: Language dropdown not found');
        console.log('All absolute positioned divs:', dropdowns);
        console.log('All divs containing language text:',
          Array.from(document.querySelectorAll('div')).filter(div =>
            div.textContent?.includes('Tiếng Việt') || div.textContent?.includes('English')
          )
        );
      }
    }, 300);
  } else {
    console.log('❌ Language button not found');
    console.log('All buttons:', buttons);
    console.log('Button texts:', Array.from(buttons).map(btn => btn.textContent));
  }
}, 2000);
